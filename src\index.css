@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

@layer components {
  /* App Layout */
  .app {
    @apply min-h-screen bg-gray-50;
  }

  /* Navigation */
  .navbar {
    @apply bg-white shadow-sm py-2 lg:py-4;
  }

  .nav-container {
    @apply max-w-screen-xl mx-auto px-4 flex justify-between items-center;
  }

  .logo {
    @apply text-xl lg:text-2xl font-bold text-blue-600 cursor-pointer;
  }

  .nav-buttons {
    @apply flex gap-2 lg:gap-4 items-center;
  }

  .nav-button {
    @apply bg-transparent border-0 text-gray-700 cursor-pointer px-3 py-2 rounded-md transition-colors text-sm lg:text-base hover:text-blue-600;
  }

  .nav-button.primary {
    @apply bg-blue-600 text-white hover:bg-blue-700;
  }

  /* Main Layout */
  .main-container {
    @apply max-w-screen-xl mx-auto px-4 py-4 lg:py-8;
  }

  /* Hero Section */
  .hero {
    @apply text-center py-6 lg:py-12 max-w-4xl mx-auto;
  }

  .hero h1 {
    @apply text-3xl lg:text-5xl font-bold text-gray-900 mb-4 lg:mb-6;
  }

  .hero p {
    @apply text-base lg:text-xl text-gray-600 mb-6 lg:mb-8 leading-relaxed;
  }

  .hero-buttons {
    @apply flex flex-col sm:flex-row gap-4 justify-center items-center;
  }

  .hero-button {
    @apply px-8 py-3 text-lg font-semibold rounded-2xl border-0 cursor-pointer transition-all duration-200 w-full sm:w-auto;
  }

  .hero-button.primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 shadow-lg hover:shadow-xl;
  }

  .hero-button.secondary {
    @apply bg-white text-blue-600 border-2 border-blue-600 hover:bg-blue-50 shadow-lg hover:shadow-xl;
  }

  /* Recent Discs Section */
  .recent-discs-section {
    @apply mt-12 lg:mt-16 max-w-7xl mx-auto;
  }

  .recent-discs-section h2 {
    @apply text-2xl lg:text-3xl font-bold text-gray-900 mb-6 text-center;
  }

  .loading-message {
    @apply text-center text-gray-600 py-8;
  }

  .no-results {
    @apply text-center text-gray-600 py-8;
  }

  .disc-details {
    @apply space-y-2 text-sm text-gray-600 mb-4;
  }

  .disc-details p {
    @apply mb-1;
  }

  .disc-details strong {
    @apply font-medium text-gray-800;
  }

  .disc-images {
    @apply grid grid-cols-2 sm:grid-cols-3 gap-2 mt-4;
  }

  .disc-image {
    @apply w-full h-24 sm:h-32 object-cover rounded border border-gray-200;
  }

  .more-images {
    @apply flex items-center justify-center bg-gray-100 text-gray-600 text-xs font-medium rounded border border-gray-200 h-24 sm:h-32;
  }

  .view-all-container {
    @apply text-center mt-8;
  }

  .view-all-button {
    @apply px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors duration-200 shadow-md hover:shadow-lg;
  }

  /* Features Section */
  .features {
    @apply grid grid-cols-1 lg:grid-cols-3 gap-8 py-12 lg:py-16;
  }

  .feature-card {
    @apply text-center p-6 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-200;
  }

  .feature-icon {
    @apply w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 text-3xl;
  }

  .feature-card h3 {
    @apply text-xl font-semibold mb-2 text-gray-900;
  }

  .feature-card p {
    @apply text-gray-600 leading-relaxed;
  }

  /* Stats Section */
  .stats {
    @apply bg-white rounded-2xl shadow-lg p-6 md:p-8 my-12;
  }

  .stats-grid {
    @apply grid grid-cols-1 md:grid-cols-3 gap-8 text-center;
  }

  .stat-item {
    @apply p-4;
  }

  .stat-number {
    @apply text-3xl lg:text-4xl font-bold text-blue-600 mb-2;
  }

  .stat-label {
    @apply text-gray-600 text-sm;
  }

  /* CTA Section */
  .cta {
    @apply bg-blue-600 text-white rounded-2xl p-8 text-center my-12;
  }

  .cta h2 {
    @apply text-2xl lg:text-3xl font-bold mb-4;
  }

  .cta p {
    @apply text-blue-100 mb-6 leading-relaxed;
  }

  .cta-button {
    @apply bg-white text-blue-600 px-6 py-3 rounded-2xl border-0 font-semibold cursor-pointer transition-colors duration-200 hover:bg-gray-100;
  }

  /* Form Layout */
  .form-container, .page-container {
    @apply max-w-4xl mx-auto px-4;
  }

  /* Form Headers */
  .form-header, .page-header {
    @apply mb-6 lg:mb-8;
  }

  .back-button {
    @apply bg-transparent border-0 text-blue-600 cursor-pointer text-sm mb-3 lg:mb-4 py-2 px-0 hover:underline;
  }

  .form-header h1, .page-header h1 {
    @apply text-2xl lg:text-3xl font-bold text-gray-900 mb-2;
  }

  .form-header p, .page-header p {
    @apply text-gray-600 text-sm lg:text-base leading-relaxed;
  }

  /* Form Components */
  .disc-form {
    @apply bg-white rounded-2xl shadow-lg p-4 md:p-6;
  }

  .form-section {
    @apply mb-8 pb-8 border-b border-gray-200 last:border-b-0 last:mb-0 last:pb-0;
  }

  .form-section h3 {
    @apply text-xl font-semibold text-gray-900 mb-4;
  }

  .form-section-description {
    @apply mb-4 text-gray-600 text-sm leading-relaxed;
  }

  .form-row {
    @apply grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4;
  }

  .form-group {
    @apply flex flex-col space-y-2;
  }

  .form-group label {
    @apply font-medium text-gray-700 text-sm;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md text-sm transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
  }

  /* Mobile form improvements */
  @media (max-width: 768px) {
    .form-group input,
    .form-group select,
    .form-group textarea {
      @apply text-base min-h-[44px] py-3;
    }

    .form-group input[type="text"],
    .form-group input[type="email"],
    .form-group input[type="tel"] {
      @apply text-base;
    }

    /* Ensure buttons are touch-friendly */
    .button,
    .hero-button,
    .nav-button {
      @apply min-h-[44px] px-4 py-3 text-base;
    }

    /* Prevent zoom on input focus */
    input[type="text"],
    input[type="email"],
    input[type="tel"],
    input[type="search"],
    select,
    textarea {
      font-size: 16px !important;
    }
  }

  .form-group textarea {
    @apply resize-y min-h-[100px];
  }

  /* Form Actions */
  .form-actions {
    @apply flex flex-col sm:flex-row gap-3 justify-end mt-6 pt-6 border-t border-gray-200;
  }

  .button {
    @apply px-4 py-2 rounded-md font-semibold cursor-pointer transition-all duration-200 border-0 text-sm w-full sm:w-auto;
  }

  .button.primary {
    @apply bg-blue-600 text-white hover:bg-blue-700;
  }

  .button.secondary {
    @apply bg-white text-gray-700 border border-gray-300 hover:bg-gray-50;
  }

  .button:disabled {
    @apply opacity-50 cursor-not-allowed;
  }

  .button.small {
    @apply px-3 py-1 text-xs;
  }

  /* Status Messages */
  .status-message {
    @apply p-4 rounded-md mb-4 font-medium;
  }

  .status-message.success {
    @apply bg-green-50 text-green-800 border border-green-200;
  }

  .status-message.error {
    @apply bg-red-50 text-red-800 border border-red-200;
  }

  /* Coming Soon */
  .coming-soon {
    @apply text-center p-12 bg-white rounded-2xl shadow-lg;
  }

  .coming-soon h2 {
    @apply text-2xl text-gray-900 mb-4;
  }

  .coming-soon p {
    @apply text-gray-600;
  }

  /* Search Components */
  .search-container {
    @apply max-w-6xl mx-auto;
  }

  .search-form {
    @apply bg-white rounded-2xl shadow-lg p-4 md:p-6 mb-6;
  }

  .search-section {
    @apply mb-6;
  }

  .search-section h3 {
    @apply text-lg lg:text-xl font-semibold text-gray-900 mb-3;
  }

  .search-actions {
    @apply flex flex-col sm:flex-row gap-3 justify-end pt-6 border-t border-gray-200;
  }

  /* Search Results */
  .search-results {
    @apply mt-6;
  }

  .search-results-header {
    @apply mb-6;
  }

  .search-results h3 {
    @apply text-lg lg:text-xl font-semibold text-gray-900 mb-4;
  }

  .search-options {
    @apply flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between bg-gray-50 p-4 rounded-lg;
  }

  .pagination-options {
    @apply flex flex-col sm:flex-row gap-3 items-start sm:items-center;
  }

  .pagination-options label {
    @apply flex items-center gap-2 text-sm text-gray-700 cursor-pointer;
  }

  .pagination-options input[type="checkbox"] {
    @apply w-4 h-4;
  }

  .pagination-options select {
    @apply px-3 py-1 border border-gray-300 rounded text-sm;
  }

  .results-info {
    @apply text-sm text-gray-600;
  }

  /* Pagination Controls */
  .pagination-controls {
    @apply flex items-center justify-center gap-2 mt-6 pt-6 border-t border-gray-200;
  }

  .page-numbers {
    @apply flex items-center gap-1;
  }

  .page-button {
    @apply px-3 py-2 text-sm border border-gray-300 bg-white text-gray-700 rounded cursor-pointer transition-colors duration-200 hover:bg-gray-50;
  }

  .page-button.active {
    @apply bg-blue-600 text-white border-blue-600 hover:bg-blue-700;
  }

  .page-button:disabled {
    @apply opacity-50 cursor-not-allowed hover:bg-white;
  }

  .page-ellipsis {
    @apply px-2 py-2 text-sm text-gray-500;
  }

  .disc-grid {
    @apply grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4;
  }

  .disc-card {
    @apply bg-white rounded-2xl shadow-lg p-4 md:p-6 transition-shadow duration-200 hover:shadow-xl;
  }

  .disc-header {
    @apply mb-4 pb-4 border-b border-gray-200;
  }

  .disc-meta {
    @apply flex gap-2 items-center flex-wrap mt-2;
  }

  .disc-header h4 {
    @apply text-lg font-semibold text-gray-900 m-0;
  }

  .disc-images {
    @apply flex gap-2 mb-4 pb-4 border-b border-gray-200;
  }

  .disc-image {
    @apply w-24 h-24 lg:w-32 lg:h-32 object-cover rounded-md border border-gray-200 bg-gray-50;
  }

  .disc-type {
    @apply bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium capitalize;
  }

  .rack-id {
    @apply bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-semibold border border-green-200;
  }

  .disc-details {
    @apply mb-6 space-y-4;
  }

  .detail-row {
    @apply flex flex-col sm:flex-row sm:justify-between gap-1;
  }

  .detail-row .label {
    @apply font-medium text-gray-600 text-sm;
  }

  .detail-row .value {
    @apply text-gray-900 text-sm break-words;
  }

  .disc-actions {
    @apply flex flex-col sm:flex-row gap-2;
  }

  .disc-actions .button {
    @apply flex-1;
  }

  /* Authentication */
  .auth-notice {
    @apply bg-blue-50 border border-blue-200 rounded-2xl p-6 mb-8 text-center;
  }

  .auth-notice p {
    @apply mb-4 text-blue-800;
  }

  .user-menu {
    @apply flex items-center gap-4;
  }

  .user-info {
    @apply text-sm text-gray-600;
  }

  .auth-container {
    @apply max-w-md mx-auto bg-white rounded-2xl shadow-lg overflow-hidden;
  }

  .auth-tabs {
    @apply flex border-b border-gray-200;
  }

  .auth-tab {
    @apply flex-1 p-4 bg-gray-50 border-0 cursor-pointer font-medium text-gray-600 transition-all duration-200 hover:bg-gray-100;
  }

  .auth-tab.active {
    @apply bg-white text-blue-600 border-b-2 border-blue-600 hover:bg-white;
  }

  .auth-form {
    @apply p-8;
  }

  .auth-required {
    @apply text-center p-12 bg-white rounded-2xl shadow-lg;
  }

  .auth-required h2 {
    @apply text-2xl text-gray-900 mb-4;
  }

  .auth-required p {
    @apply text-gray-600 mb-8 leading-relaxed;
  }

  .button.full-width {
    @apply w-full;
  }

  /* Loading */
  .loading-container {
    @apply flex flex-col items-center justify-center min-h-[50vh] text-center;
  }

  .loading-spinner {
    @apply w-8 h-8 border-2 border-gray-200 border-t-blue-600 rounded-full animate-spin mb-4;
  }
}
