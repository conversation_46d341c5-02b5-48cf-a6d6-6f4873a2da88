@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {

  /* RakerDiver Dashboard */
  .dashboard-actions {
    @apply mb-6 flex flex-wrap gap-3;
  }

  .dashboard-actions .button {
    @apply m-0;
  }

  .turnin-grid {
    @apply grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 mt-4;
  }

  .turnin-card {
    @apply bg-white border border-gray-200 rounded-2xl p-4 md:p-6 shadow-lg transition-shadow duration-200 hover:shadow-xl;
  }

  .turnin-header {
    @apply flex justify-between items-start mb-4 pb-4 border-b border-gray-200;
  }

  .turnin-header h4 {
    @apply text-lg font-semibold text-gray-900 m-0;
  }

  .status-badge {
    @apply px-2 py-1 rounded text-xs font-medium capitalize;
  }

  .status-badge.verified {
    @apply bg-green-100 text-green-800;
  }

  .status-badge.pending {
    @apply bg-yellow-100 text-yellow-800;
  }

  .turnin-details {
    @apply mb-6 space-y-2;
  }

  .turnin-details p {
    @apply flex justify-between text-sm;
  }

  .turnin-details strong {
    @apply font-medium text-gray-600;
  }

  .payment-summary {
    @apply mt-4 pt-4 border-t border-gray-200;
  }

  /* Dashboard Sections */
  .dashboard-section {
    @apply mb-8;
  }

  .dashboard-section h2 {
    @apply text-xl font-semibold text-gray-900 mb-4;
  }

  /* Admin Bulk Turn-ins */
  .admin-actions {
    @apply mb-8 flex flex-wrap gap-4;
  }

  .admin-turnin-grid {
    @apply grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 mt-4;
  }

  .admin-turnin-card {
    @apply bg-white border border-gray-200 rounded-2xl p-6 shadow-lg;
  }

  .turnin-actions {
    @apply mt-4 flex flex-col gap-3;
  }

  .verification-section {
    @apply flex flex-col gap-3 p-4 bg-gray-50 rounded-xl border border-gray-200;
  }

  .verification-section textarea {
    @apply resize-y min-h-[80px] px-3 py-2 border border-gray-300 rounded-md text-sm;
  }

  /* Modal Components */
  .modal-overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50 p-4;
  }

  .modal-content {
    @apply bg-white rounded-2xl p-6 md:p-8 max-w-2xl w-full max-h-[90vh] overflow-y-auto relative shadow-2xl;
  }

  .modal-content.large-modal {
    @apply max-w-6xl;
  }

  .modal-header {
    @apply flex justify-between items-center mb-6 pb-4 border-b border-gray-200;
  }

  .modal-header h3 {
    @apply text-xl font-semibold text-gray-900 m-0;
  }

  .close-button {
    @apply bg-transparent border-0 text-2xl cursor-pointer text-gray-500 p-1 w-8 h-8 flex items-center justify-center rounded transition-all duration-200 hover:text-gray-700 hover:bg-gray-100;
  }

  .modal-body {
    @apply max-h-[70vh] overflow-y-auto;
  }

  /* Payment Components */
  .payment-form {
    @apply bg-gray-50 p-6 rounded-2xl mb-8 border border-gray-200;
  }

  .payment-form h4 {
    @apply text-lg font-semibold text-gray-900 mt-0 mb-4;
  }

  .payments-section h4 {
    @apply text-lg font-semibold text-gray-900 mb-4;
  }

  .payments-list {
    @apply flex flex-col gap-4;
  }

  .payment-item {
    @apply flex flex-col lg:flex-row lg:justify-between lg:items-start gap-4 p-4 bg-white rounded-2xl border border-gray-200 shadow-lg;
  }

  .payment-details {
    @apply flex-1;
  }

  .payment-details p {
    @apply my-1 text-sm text-gray-700;
  }

  .payment-details strong {
    @apply font-medium text-gray-600;
  }

  .payment-actions,
  .payment-status {
    @apply flex flex-col items-start lg:items-end gap-2;
  }

  .filter-select {
    @apply px-3 py-2 border border-gray-300 rounded-md bg-white text-sm text-gray-700;
  }

  /* Button Aliases */
  .primary-button {
    @apply px-4 py-2 rounded-md font-semibold cursor-pointer transition-all duration-200 border-0 text-sm bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .secondary-button {
    @apply px-4 py-2 rounded-md font-semibold cursor-pointer transition-all duration-200 text-sm bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  /* Source Management */
  .form-select {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md text-base bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
  }

  .form-help {
    @apply block mt-1 text-sm text-gray-600;
  }

  .admin-actions {
    @apply flex gap-4 mb-8 flex-wrap;
  }

  .admin-actions .button {
    @apply m-0;
  }

  /* Large Modal Override */
  .large-modal {
    @apply max-w-[90vw] w-full max-w-7xl max-h-[90vh];
  }

  .large-modal .modal-body {
    @apply max-h-[calc(90vh-120px)] overflow-y-auto;
  }

  /* Additional Status Badge Styles */
  .status-badge.verified {
    @apply bg-green-100 text-green-800;
  }

  .status-badge.pending {
    @apply bg-red-100 text-red-800;
  }

  /* Sources Table Styling */
  .sources-table {
    @apply border border-gray-200 rounded-lg overflow-hidden bg-white;
  }

  .sources-header {
    @apply grid gap-4 p-4 bg-gray-50 border-b border-gray-200 font-semibold text-sm text-gray-700 uppercase tracking-wide;
    grid-template-columns: 2fr 120px 80px 2fr 160px;
  }

  .source-row {
    @apply grid gap-4 p-4 border-b border-gray-100 items-center transition-colors hover:bg-gray-50;
    grid-template-columns: 2fr 120px 80px 2fr 160px;
  }

  .source-row:last-child {
    @apply border-b-0;
  }

  .source-col-name {
    @apply flex flex-col gap-1;
  }

  .source-name {
    @apply font-semibold text-gray-900 text-base;
  }

  .source-description {
    @apply text-sm text-gray-600 leading-relaxed;
  }

  .source-col-status {
    @apply flex justify-center;
  }

  .status-badge.active {
    @apply bg-green-100 text-green-800 border border-green-200;
  }

  .status-badge.inactive {
    @apply bg-red-100 text-red-800 border border-red-200;
  }

  .source-col-order {
    @apply text-center;
  }

  .order-number {
    @apply inline-block bg-gray-100 text-gray-700 px-2 py-1 rounded font-medium text-sm;
  }

  .source-col-message {
    @apply flex flex-col gap-1;
  }

  .message-preview {
    @apply flex flex-col gap-1;
  }

  .message-text {
    @apply text-sm text-gray-700 leading-relaxed;
  }

  .message-indicator {
    @apply text-xs text-green-600 font-medium;
  }

  .no-message {
    @apply text-sm text-gray-400 italic;
  }

  .source-col-actions {
    @apply flex gap-2 justify-end;
  }

  .source-edit-form {
    @apply col-span-full bg-gray-50 p-5 rounded-lg border-2 border-gray-200;
  }

  .empty-state {
    @apply text-center py-10 px-5 text-gray-600 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300;
  }

  .empty-state p {
    @apply m-0 text-base;
  }

  /* Mobile Navigation Fixes */
  @media (max-width: 768px) {
    .nav-container {
      @apply flex-col items-start gap-3 px-4 py-3;
    }

    .nav-buttons {
      @apply w-full justify-start flex-wrap gap-2;
    }

    .user-menu {
      @apply w-full justify-start flex-wrap gap-2;
    }

    .user-info {
      @apply w-full text-xs mb-2;
    }

    /* Ensure buttons are large enough for touch */
    .nav-button {
      @apply min-h-[44px] px-4 py-2 text-base;
    }

    /* Make sure profile and other nav buttons are visible and clickable */
    .user-menu .nav-button {
      @apply bg-white border border-gray-300 shadow-sm;
    }

    .user-menu .nav-button:hover {
      @apply bg-gray-50;
    }
  }

  /* Disc Card Styles */
  .disc-grid {
    @apply grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3;
  }

  .disc-card {
    @apply bg-white rounded-lg shadow-md border border-gray-200 p-6 hover:shadow-lg transition-shadow;
  }

  .disc-header {
    @apply mb-4;
  }

  .disc-header h4 {
    @apply text-lg font-semibold text-gray-900 mb-2;
  }

  .disc-meta {
    @apply flex flex-wrap gap-2;
  }

  .disc-type {
    @apply inline-block bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded;
  }

  .rack-id {
    @apply inline-block bg-gray-100 text-gray-800 text-xs font-medium px-2 py-1 rounded;
  }

  .disc-images {
    @apply grid grid-cols-2 gap-2 mt-4;
  }

  .disc-image {
    @apply w-full h-32 object-cover rounded border;
  }

  /* Source Card Styles (similar to disc cards) */
  .source-grid {
    @apply grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3;
  }

  .source-card {
    @apply bg-white rounded-lg shadow-md border border-gray-200 p-6 hover:shadow-lg transition-shadow;
  }

  .source-card-header {
    @apply mb-4;
  }

  .source-card-header h4 {
    @apply text-lg font-semibold text-gray-900 mb-2;
  }

  .source-card-meta {
    @apply flex flex-wrap gap-2 mb-3;
  }

  .source-card-description {
    @apply text-sm text-gray-600 mb-4 leading-relaxed;
  }

  .source-card-message {
    @apply mb-4;
  }

  .source-card-message-label {
    @apply text-sm font-medium text-gray-700 mb-2 block;
  }

  .source-card-message-text {
    @apply text-sm text-gray-600 bg-gray-50 p-3 rounded border leading-relaxed;
  }

  .source-card-message-empty {
    @apply text-sm text-gray-400 italic bg-gray-50 p-3 rounded border;
  }

  .source-card-actions {
    @apply flex gap-2 pt-4 border-t border-gray-100;
  }

  /* Responsive design for sources */
  @media (max-width: 1024px) {
    .sources-table {
      @apply hidden;
    }

    .sources-mobile {
      @apply block;
    }
  }

  @media (min-width: 1025px) {
    .sources-table {
      @apply block;
    }

    .sources-mobile {
      @apply hidden;
    }
  }

  /* Report Found Disc Form Styles */
  .critical-section {
    @apply border-l-4 border-blue-500 bg-blue-50;
  }

  .critical-section h3 {
    @apply text-blue-900;
  }

  .quick-report {
    @apply bg-gray-50 p-6 rounded-lg border-2 border-dashed border-gray-300 text-center;
  }

  .quick-report .button.large {
    @apply px-8 py-3 text-lg font-semibold;
  }

  .quick-report .button:not(:last-child) {
    @apply mr-4;
  }

  .final-actions {
    @apply mt-8 pt-6 border-t border-gray-200;
  }

  .form-section-description {
    @apply text-sm text-gray-600 mb-4;
  }
}
